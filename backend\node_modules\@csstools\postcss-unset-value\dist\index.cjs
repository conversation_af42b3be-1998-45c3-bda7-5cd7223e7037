"use strict";const e=new Set(["block-ellipsis","border-boundary","border-collapse","border-spacing","box-snap","caption-side","caret","caret-shape","clip-rule","color","color-interpolation","color-interpolation-filters","cursor","direction","dominant-baseline","empty-cells","fill","fill-color","fill-image","fill-opacity","fill-position","fill-repeat","fill-rule","fill-size","font","font-family","font-feature-settings","font-kerning","font-language-override","font-palette","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-emoji","font-variant-ligatures","font-weight","forced-color-adjust","hyphenate-character","hyphenate-limit-chars","hyphenate-limit-last","hyphenate-limit-lines","hyphenate-limit-zone","hyphens","image-orientation","image-rendering","image-resolution","initial-letter-wrap","letter-spacing","line-break","line-height","line-padding","line-snap","list-style","list-style-image","list-style-position","list-style-type","marker","marker-end","marker-mid","marker-side","marker-start","orphans","overflow-wrap","paint-order","pointer-events","print-color-adjust","quotes","ruby-merge","ruby-overhang","ruby-position","shape-rendering","speak","speak-as","stroke","stroke-align","stroke-alignment","stroke-color","stroke-dash-corner","stroke-dash-justify","stroke-dashadjust","stroke-dasharray","stroke-dashcorner","stroke-dashoffset","stroke-image","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-position","stroke-repeat","stroke-size","stroke-width","tab-size","text-align","text-align-all","text-align-last","text-anchor","text-combine-upright","text-decoration-skip","text-decoration-skip-box","text-decoration-skip-inset","text-decoration-skip-self","text-decoration-skip-spaces","text-edge","text-emphasis-color","text-emphasis-position","text-emphasis-skip","text-emphasis-style","text-indent","text-justify","text-orientation","text-rendering","text-shadow","text-size-adjust","text-space-collapse","text-spacing","text-transform","text-underline-position","text-wrap","visibility","voice-balance","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","white-space","widows","word-boundary-detection","word-boundary-expansion","word-break","word-spacing","word-wrap","writing-mode"]),t=new Set(["align-content","align-items","align-self","alignment-baseline","animation","animation-composition","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timeline","animation-timing-function","appearance","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-block","background-position-inline","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","baseline-source","block-step","block-step-align","block-step-insert","block-step-round","block-step-size","bookmark-label","bookmark-level","bookmark-state","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-clip","border-clip-bottom","border-clip-left","border-clip-right","border-clip-top","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-limit","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","clear","clip","clip-path","color-adjust","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-width","container","container-name","container-type","content","continue","copy-into","corner-shape","corners","counter-increment","counter-reset","cue","cue-after","cue-before","cx","cy","d","display","fill-break","fill-origin","filter","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-defer","float-offset","float-reference","flood-color","flood-opacity","flow-from","flow-into","footnote-display","footnote-policy","gap","glyph-orientation-vertical","grid-auto-columns","grid-auto-rows","grid-column-gap","grid-gap","grid-row-gap","grid-template-columns","grid-template-rows","height","initial-letter","inline-sizing","input-security","isolation","justify-content","justify-items","justify-self","leading-trim","left","lighting-color","line-clamp","line-grid","margin","margin-bottom","margin-break","margin-left","margin-right","margin-top","mask","mask-border","mask-border-mode","mask-border-outset","mask-border-repeat","mask-border-slice","mask-border-source","mask-border-width","mask-clip","mask-image","mask-origin","mask-position","mask-repeat","mask-size","mask-type","max-height","max-lines","max-width","min-height","min-intrinsic-sizing","min-width","mix-blend-mode","nav-down","nav-left","nav-right","nav-up","object-fit","object-position","opacity","order","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page-break-after","page-break-before","page-break-inside","pause","pause-after","pause-before","perspective","perspective-origin","place-content","position","r","region-fragment","resize","rest","rest-after","rest-before","right","row-gap","rx","ry","scroll-snap-type","shape-image-threshold","shape-inside","shape-margin","shape-outside","shape-padding","shape-subtract","spatial-navigation-action","spatial-navigation-contain","spatial-navigation-function","stop-color","stop-opacity","string-set","stroke-break","stroke-origin","table-layout","text-decoration","text-decoration-color","text-decoration-line","text-decoration-style","text-emphasis","text-group-align","text-overflow","text-space-trim","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","unicode-bidi","user-select","vector-effect","vertical-align","voice-duration","width","will-change","wrap-after","wrap-before","wrap-flow","wrap-inside","wrap-through","x","y","z-index"]),o=o=>{const i=Object.assign({preserve:!1},o);return{postcssPlugin:"postcss-unset-value",Declaration(o){if("unset"!==o.value.toLowerCase())return;let r=!1;e.has(o.prop.toLowerCase())?r="inherit":t.has(o.prop.toLowerCase())&&(r="initial"),r&&(o.cloneBefore({prop:o.prop,value:r}),i.preserve||o.remove())}}};o.postcss=!0,module.exports=o;
