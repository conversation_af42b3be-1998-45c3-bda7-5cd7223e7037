{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_path", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "addDisplayName", "id", "call", "props", "arguments", "properties", "safe", "i", "length", "prop", "t", "isSpreadElement", "key", "toCom<PERSON><PERSON>ey", "isStringLiteral", "value", "unshift", "objectProperty", "identifier", "stringLiteral", "isCreateClassCallExpression", "buildMatchMemberExpression", "isCreateClassAddon", "callee", "isIdentifier", "name", "isCreateClass", "node", "isCallExpression", "args", "first", "isObjectExpression", "visitor", "ExportDefaultDeclaration", "state", "declaration", "filename", "displayName", "path", "basename", "extname", "dirname", "CallExpression", "find", "isAssignmentExpression", "left", "isObjectProperty", "isVariableDeclarator", "isStatement", "isMemberExpression", "property"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport path from \"node:path\";\nimport { types as t } from \"@babel/core\";\n\ntype ReactCreateClassCall = t.CallExpression & {\n  arguments: [t.ObjectExpression];\n};\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  function addDisplayName(id: string, call: ReactCreateClassCall) {\n    const props = call.arguments[0].properties;\n    let safe = true;\n\n    for (let i = 0; i < props.length; i++) {\n      const prop = props[i];\n      if (t.isSpreadElement(prop)) {\n        continue;\n      }\n      const key = t.toComputedKey(prop);\n      if (t.isStringLiteral(key, { value: \"displayName\" })) {\n        safe = false;\n        break;\n      }\n    }\n\n    if (safe) {\n      props.unshift(\n        t.objectProperty(t.identifier(\"displayName\"), t.stringLiteral(id)),\n      );\n    }\n  }\n\n  const isCreateClassCallExpression =\n    t.buildMatchMemberExpression(\"React.createClass\");\n  const isCreateClassAddon = (callee: t.CallExpression[\"callee\"]) =>\n    t.isIdentifier(callee, { name: \"createReactClass\" });\n\n  function isCreateClass(node?: t.Node): node is ReactCreateClassCall {\n    if (!node || !t.isCallExpression(node)) return false;\n\n    // not createReactClass nor React.createClass call member object\n    if (\n      !isCreateClassCallExpression(node.callee) &&\n      !isCreateClassAddon(node.callee)\n    ) {\n      return false;\n    }\n\n    // no call arguments\n    const args = node.arguments;\n    if (args.length !== 1) return false;\n\n    // first node arg is not an object\n    const first = args[0];\n    if (!t.isObjectExpression(first)) return false;\n\n    return true;\n  }\n\n  return {\n    name: \"transform-react-display-name\",\n\n    visitor: {\n      ExportDefaultDeclaration({ node }, state) {\n        if (isCreateClass(node.declaration)) {\n          const filename = state.filename || \"unknown\";\n\n          let displayName = path.basename(filename, path.extname(filename));\n\n          // ./{module name}/index.js\n          if (displayName === \"index\") {\n            displayName = path.basename(path.dirname(filename));\n          }\n\n          addDisplayName(displayName, node.declaration);\n        }\n      },\n\n      CallExpression(path) {\n        const { node } = path;\n        if (!isCreateClass(node)) return;\n\n        let id: t.LVal | t.Expression | t.PrivateName | null;\n\n        // crawl up the ancestry looking for possible candidates for displayName inference\n        path.find(function (path) {\n          if (path.isAssignmentExpression()) {\n            id = path.node.left;\n          } else if (path.isObjectProperty()) {\n            id = path.node.key;\n          } else if (path.isVariableDeclarator()) {\n            id = path.node.id;\n          } else if (path.isStatement()) {\n            // we've hit a statement, we should stop crawling up\n            return true;\n          }\n\n          // we've got an id! no need to continue\n          if (id) return true;\n        });\n\n        // ensure that we have an identifier we can inherit from\n        if (!id) return;\n\n        // foo.bar -> bar\n        if (t.isMemberExpression(id)) {\n          id = id.property;\n        }\n\n        // identifiers are the only thing we can reliably get a name from\n        if (t.isIdentifier(id)) {\n          addDisplayName(id.name, node);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAAyC,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAM1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,SAASC,cAAcA,CAACC,EAAU,EAAEC,IAA0B,EAAE;IAC9D,MAAMC,KAAK,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,CAACC,UAAU;IAC1C,IAAIC,IAAI,GAAG,IAAI;IAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAME,IAAI,GAAGN,KAAK,CAACI,CAAC,CAAC;MACrB,IAAIG,WAAC,CAACC,eAAe,CAACF,IAAI,CAAC,EAAE;QAC3B;MACF;MACA,MAAMG,GAAG,GAAGF,WAAC,CAACG,aAAa,CAACJ,IAAI,CAAC;MACjC,IAAIC,WAAC,CAACI,eAAe,CAACF,GAAG,EAAE;QAAEG,KAAK,EAAE;MAAc,CAAC,CAAC,EAAE;QACpDT,IAAI,GAAG,KAAK;QACZ;MACF;IACF;IAEA,IAAIA,IAAI,EAAE;MACRH,KAAK,CAACa,OAAO,CACXN,WAAC,CAACO,cAAc,CAACP,WAAC,CAACQ,UAAU,CAAC,aAAa,CAAC,EAAER,WAAC,CAACS,aAAa,CAAClB,EAAE,CAAC,CACnE,CAAC;IACH;EACF;EAEA,MAAMmB,2BAA2B,GAC/BV,WAAC,CAACW,0BAA0B,CAAC,mBAAmB,CAAC;EACnD,MAAMC,kBAAkB,GAAIC,MAAkC,IAC5Db,WAAC,CAACc,YAAY,CAACD,MAAM,EAAE;IAAEE,IAAI,EAAE;EAAmB,CAAC,CAAC;EAEtD,SAASC,aAAaA,CAACC,IAAa,EAAgC;IAClE,IAAI,CAACA,IAAI,IAAI,CAACjB,WAAC,CAACkB,gBAAgB,CAACD,IAAI,CAAC,EAAE,OAAO,KAAK;IAGpD,IACE,CAACP,2BAA2B,CAACO,IAAI,CAACJ,MAAM,CAAC,IACzC,CAACD,kBAAkB,CAACK,IAAI,CAACJ,MAAM,CAAC,EAChC;MACA,OAAO,KAAK;IACd;IAGA,MAAMM,IAAI,GAAGF,IAAI,CAACvB,SAAS;IAC3B,IAAIyB,IAAI,CAACrB,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IAGnC,MAAMsB,KAAK,GAAGD,IAAI,CAAC,CAAC,CAAC;IACrB,IAAI,CAACnB,WAAC,CAACqB,kBAAkB,CAACD,KAAK,CAAC,EAAE,OAAO,KAAK;IAE9C,OAAO,IAAI;EACb;EAEA,OAAO;IACLL,IAAI,EAAE,8BAA8B;IAEpCO,OAAO,EAAE;MACPC,wBAAwBA,CAAC;QAAEN;MAAK,CAAC,EAAEO,KAAK,EAAE;QACxC,IAAIR,aAAa,CAACC,IAAI,CAACQ,WAAW,CAAC,EAAE;UACnC,MAAMC,QAAQ,GAAGF,KAAK,CAACE,QAAQ,IAAI,SAAS;UAE5C,IAAIC,WAAW,GAAGC,KAAI,CAACC,QAAQ,CAACH,QAAQ,EAAEE,KAAI,CAACE,OAAO,CAACJ,QAAQ,CAAC,CAAC;UAGjE,IAAIC,WAAW,KAAK,OAAO,EAAE;YAC3BA,WAAW,GAAGC,KAAI,CAACC,QAAQ,CAACD,KAAI,CAACG,OAAO,CAACL,QAAQ,CAAC,CAAC;UACrD;UAEApC,cAAc,CAACqC,WAAW,EAAEV,IAAI,CAACQ,WAAW,CAAC;QAC/C;MACF,CAAC;MAEDO,cAAcA,CAACJ,IAAI,EAAE;QACnB,MAAM;UAAEX;QAAK,CAAC,GAAGW,IAAI;QACrB,IAAI,CAACZ,aAAa,CAACC,IAAI,CAAC,EAAE;QAE1B,IAAI1B,EAAgD;QAGpDqC,IAAI,CAACK,IAAI,CAAC,UAAUL,IAAI,EAAE;UACxB,IAAIA,IAAI,CAACM,sBAAsB,CAAC,CAAC,EAAE;YACjC3C,EAAE,GAAGqC,IAAI,CAACX,IAAI,CAACkB,IAAI;UACrB,CAAC,MAAM,IAAIP,IAAI,CAACQ,gBAAgB,CAAC,CAAC,EAAE;YAClC7C,EAAE,GAAGqC,IAAI,CAACX,IAAI,CAACf,GAAG;UACpB,CAAC,MAAM,IAAI0B,IAAI,CAACS,oBAAoB,CAAC,CAAC,EAAE;YACtC9C,EAAE,GAAGqC,IAAI,CAACX,IAAI,CAAC1B,EAAE;UACnB,CAAC,MAAM,IAAIqC,IAAI,CAACU,WAAW,CAAC,CAAC,EAAE;YAE7B,OAAO,IAAI;UACb;UAGA,IAAI/C,EAAE,EAAE,OAAO,IAAI;QACrB,CAAC,CAAC;QAGF,IAAI,CAACA,EAAE,EAAE;QAGT,IAAIS,WAAC,CAACuC,kBAAkB,CAAChD,EAAE,CAAC,EAAE;UAC5BA,EAAE,GAAGA,EAAE,CAACiD,QAAQ;QAClB;QAGA,IAAIxC,WAAC,CAACc,YAAY,CAACvB,EAAE,CAAC,EAAE;UACtBD,cAAc,CAACC,EAAE,CAACwB,IAAI,EAAEE,IAAI,CAAC;QAC/B;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}